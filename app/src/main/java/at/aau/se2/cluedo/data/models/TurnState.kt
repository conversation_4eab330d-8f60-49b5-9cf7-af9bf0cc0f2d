package at.aau.se2.cluedo.data.models

import com.google.gson.annotations.SerializedName


enum class TurnState(val value: String) {
    @SerializedName("WAITING_FOR_PLAYERS")
    WAITING_FOR_PLAYERS("WAITING_FOR_PLAYERS"),
    
    @SerializedName("WAITING_FOR_START")
    WAITING_FOR_START("WAITING_FOR_START"),
    
    @SerializedName("PLAYERS_TURN_ROLL_DICE")
    PLAYERS_TURN_ROLL_DICE("PLAYERS_TURN_ROLL_DICE"),
    
    @SerializedName("PLAYERS_TURN_MOVE")
    PLAYERS_TURN_MOVE("PLAYERS_TURN_MOVE"),
    
    @SerializedName("PLAYERS_TURN_SUSPECT")
    PLAYERS_TURN_SUSPECT("PLAYERS_TURN_SUSPECT"),
    
    @SerializedName("PLAYERS_TURN_SOLVE")
    PLAYERS_TURN_SOLVE("PLAYERS_TURN_SOLVE"),
    
    @SerializedName("PLAYERS_TURN_END")
    PLAYERS_TURN_END("PLAYERS_TURN_END"),
    
    @SerializedName("PLAYER_HAS_WON")
    PLAYER_HAS_WON("PLAYER_HAS_WON");
    
    companion object {
        fun fromValue(value: String): TurnState? {
            return TurnState.entries.find { it.value == value }
        }
    }
}
