package at.aau.se2.cluedo.data.models

import com.google.gson.annotations.SerializedName

/**
 * Response DTO containing current turn state information
 */
data class TurnStateResponse(
    @SerializedName("lobbyId")
    val lobbyId: String = "",

    @SerializedName("currentPlayerName")
    val currentPlayerName: String = "",

    @SerializedName("currentPlayerIndex")
    val currentPlayerIndex: Int = 0,

    @SerializedName("turnState")
    val turnState: String = TurnState.WAITING_FOR_PLAYERS.value,

    @SerializedName("canMakeAccusation")
    val canMakeAccusation: Boolean = false,

    @SerializedName("canMakeSuggestion")
    val canMakeSuggestion: Boolean = false,

    @SerializedName("diceValue")
    val diceValue: Int = 0,

    @SerializedName("message")
    val message: String = "",

    @SerializedName("success")
    val success: Boolean = true
)

/**
 * Simple dice roll response format (legacy format from backend)
 */
data class SimpleDiceRollResponse(
    @SerializedName("player")
    val player: String = "",

    @SerializedName("turnState")
    val turnState: String = "",

    @SerializedName("diceValue")
    val diceValue: Int = 0
)
