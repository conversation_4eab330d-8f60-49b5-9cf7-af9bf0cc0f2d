package at.aau.se2.cluedo.data.network

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import android.util.Log
import at.aau.se2.cluedo.data.GameData
import at.aau.se2.cluedo.data.models.ActiveLobbiesResponse
import at.aau.se2.cluedo.data.models.CanStartGameResponse
import at.aau.se2.cluedo.data.models.CreateLobbyRequest
import at.aau.se2.cluedo.data.models.DiceResult
import at.aau.se2.cluedo.data.models.GameStartedResponse
import at.aau.se2.cluedo.data.models.GetActiveLobbiesRequest
import at.aau.se2.cluedo.data.models.IsWallRequest
import at.aau.se2.cluedo.data.models.JoinLobbyRequest
import at.aau.se2.cluedo.data.models.LeaveLobbyRequest
import at.aau.se2.cluedo.data.models.Lobby
import at.aau.se2.cluedo.data.models.LobbyStatus
import at.aau.se2.cluedo.data.models.PerformMoveResponse
import at.aau.se2.cluedo.data.models.Player
import at.aau.se2.cluedo.data.models.PlayerColor
import at.aau.se2.cluedo.data.models.SolveCaseRequest
import at.aau.se2.cluedo.data.models.StartGameRequest
import at.aau.se2.cluedo.data.models.TurnStateResponse
import at.aau.se2.cluedo.data.models.TurnActionRequest
import at.aau.se2.cluedo.data.models.SuggestionRequest
import at.aau.se2.cluedo.data.models.TurnState
import com.google.gson.Gson
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import ua.naiksoftware.stomp.Stomp
import ua.naiksoftware.stomp.StompClient
import ua.naiksoftware.stomp.dto.LifecycleEvent
import ua.naiksoftware.stomp.dto.StompMessage

class WebSocketService {
    companion object {
        private const val SERVER_IP = "********"
        private const val SERVER_PORT = "8321"
        private const val CONNECTION_URL = "ws://$SERVER_IP:$SERVER_PORT/ws"
        private const val TOPIC_LOBBY_CREATED = "/topic/lobbyCreated"
        private const val TOPIC_LOBBY_UPDATES_PREFIX = "/topic/lobby/"
        private const val APP_CREATE_LOBBY = "/app/createLobby"
        private const val APP_JOIN_LOBBY_PREFIX = "/app/joinLobby/"
        private const val APP_LEAVE_LOBBY_PREFIX = "/app/leaveLobby/"
        private const val APP_GET_ACTIVE_LOBBIES = "/app/getActiveLobbies"
        private const val TOPIC_ACTIVE_LOBBIES = "/topic/activeLobbies"
        private const val APP_CAN_START_GAME_PREFIX = "/app/canStartGame/"
        private const val TOPIC_CAN_START_GAME_PREFIX = "/topic/canStartGame/"
        private const val APP_START_GAME_PREFIX = "/app/startGame/"
        private const val TOPIC_GAME_STARTED_PREFIX = "/topic/gameStarted/"
        private const val TOPIC_GAME_DATA_PREFIX = "/topic/gameData/"
        private const val APP_GET_GAME_DATA="/app/getGameData/"

        private const val APP_IS_WALL="/app/isWall/"
        private const val TOPIC_IS_WALL="/topic/isWall/"

        private const val TOPIC_DICE_RESULT = "/topic/diceResult"
        private const val APP_ROLL_DICE = "/app/rollDice"

        // Turn-based constants
        private const val APP_INITIALIZE_TURNS = "/app/initializeTurns/"
        private const val TOPIC_TURNS_INITIALIZED = "/topic/turnsInitialized/"
        private const val APP_GET_TURN_STATE = "/app/getTurnState/"
        private const val TOPIC_CURRENT_TURN_STATE = "/topic/currentTurnState/"
        private const val APP_CHECK_PLAYER_TURN = "/app/checkPlayerTurn/"
        private const val TOPIC_PLAYER_TURN_CHECK = "/topic/playerTurnCheck/"
        private const val APP_ROLL_DICE_TURN = "/app/rollDice/"
        private const val TOPIC_DICE_ROLLED = "/topic/diceRolled/"
        private const val APP_COMPLETE_MOVEMENT = "/app/completeMovement/"
        private const val TOPIC_MOVEMENT_COMPLETED = "/topic/movementCompleted/"
        private const val APP_MAKE_SUGGESTION = "/app/makeSuggestion/"
        private const val TOPIC_SUGGESTION_MADE = "/topic/suggestionMade/"

        @Volatile private var instance: WebSocketService? = null

        fun getInstance() =
            instance ?: synchronized(this) {
                instance ?: WebSocketService().also { instance = it }
            }

        private const val TOPIC_GET_PLAYERS = "/topic/players"
        private const val APP_GET_PLAYERS = "/app/players"
        private const val APP_PERFORM_MOVE = "/app/performMovement/"
    }

    private val gson = Gson()
    private var stompClient: StompClient? = null
    private var currentLobbySubscriptionId: String? = null

    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()

    private val _lobbyState = MutableStateFlow<Lobby?>(null)
    val lobbyState: StateFlow<Lobby?> = _lobbyState.asStateFlow()

    private val _gameDataState = MutableStateFlow<GameData?>(null)
    val gameDataState = _gameDataState.asStateFlow()

    val _player = MutableStateFlow<Player?>(null)           //Client player object
    val player: StateFlow<Player?> = _player.asStateFlow()  //Client player object

    private val _createdLobbyId = MutableStateFlow<String?>(null)
    val createdLobbyId: StateFlow<String?> = _createdLobbyId.asStateFlow()

    private val _canStartGame = MutableStateFlow(false)
    val canStartGame: StateFlow<Boolean> = _canStartGame.asStateFlow()

    private val _gameStarted = MutableStateFlow(false)
    val gameStarted: StateFlow<Boolean> = _gameStarted.asStateFlow()

    private val _gameState = MutableStateFlow<GameStartedResponse?>(null)
    val gameState: StateFlow<GameStartedResponse?> = _gameState.asStateFlow()

    private val _errorMessages = MutableSharedFlow<String>(replay = 0, extraBufferCapacity = 10)
    val errorMessages: SharedFlow<String> = _errorMessages.asSharedFlow()

    // Turn-based  state flows
    private val _currentTurnState = MutableStateFlow<TurnStateResponse?>(null)
    val currentTurnState: StateFlow<TurnStateResponse?> = _currentTurnState.asStateFlow()

    private val _isCurrentPlayerTurn = MutableStateFlow(false)
    val isCurrentPlayerTurn: StateFlow<Boolean> = _isCurrentPlayerTurn.asStateFlow()

    init {
        setupStompClient()
    }

    /*private var player: Player? = null*/
    public fun getPlayer(): Player? {
        return player.value
    }
    public fun setPlayer(p:Player){
        this._player.value = p
    }

    @SuppressLint("CheckResult")
    private fun setupStompClient() {
        if (stompClient != null && _isConnected.value == true) return
        stompClient?.disconnect()

        stompClient = Stomp.over(Stomp.ConnectionProvider.OKHTTP, CONNECTION_URL)

        stompClient?.lifecycle()?.subscribe(
            { lifecycleEvent ->
                when (lifecycleEvent.type) {
                    LifecycleEvent.Type.OPENED -> {
                        _isConnected.value = true
                        subscribeToGeneralTopics()
                        _createdLobbyId.value?.takeIf { it.isNotBlank() }?.let { lobbyId ->
                            subscribeToSpecificLobbyTopics(lobbyId)
                        }
                        subscribeToDiceResultTopic()
                        subscribePlayersResult()
                    }

                    LifecycleEvent.Type.ERROR -> {
                        _errorMessages.tryEmit("Connection Error: ${lifecycleEvent.exception?.message}")
                        resetConnectionState()
                    }

                    LifecycleEvent.Type.ERROR,
                    LifecycleEvent.Type.CLOSED,
                    LifecycleEvent.Type.FAILED_SERVER_HEARTBEAT -> resetConnectionState()
                }
            },
            { resetConnectionState() }
        )
    }

    fun connect() {
        if (_isConnected.value) return
        if (stompClient == null || stompClient?.isConnected == false) {
            setupStompClient()
        }
        if (stompClient?.isConnected == false) {
            stompClient?.connect()
        }
    }

    fun disconnect() {
        stompClient?.disconnect()
        if (_isConnected.value) {
            resetConnectionState()
        }
    }

    private fun resetConnectionState() {
        _isConnected.value = false
        _lobbyState.value = null
        _canStartGame.value = false
        _gameStarted.value = false
        _gameState.value = null
        _currentTurnState.value = null
        _isCurrentPlayerTurn.value = false
    }

    @SuppressLint("CheckResult")
    private fun subscribeToGeneralTopics() {
        stompClient?.topic(TOPIC_LOBBY_CREATED)?.subscribe { stompMessage: StompMessage ->
            val newLobbyId = stompMessage.payload
            if (!newLobbyId.isNullOrBlank()) {
                _createdLobbyId.value = newLobbyId
                subscribeToSpecificLobbyTopics(newLobbyId)
            }
        }

        stompClient?.topic(TOPIC_ACTIVE_LOBBIES)?.subscribe { stompMessage: StompMessage ->
            val response = gson.fromJson(stompMessage.payload, ActiveLobbiesResponse::class.java)
            response.lobbies.firstOrNull()?.id?.takeIf { it.isNotBlank() }?.let { lobbyId ->
                if (_createdLobbyId.value.isNullOrBlank() || _createdLobbyId.value != lobbyId) {
                    _createdLobbyId.value = lobbyId
                    subscribeToSpecificLobbyTopics(lobbyId)
                }
            }
        }
    }

    @SuppressLint("CheckResult")
    fun getActiveLobbies() {
        if (!_isConnected.value) {
            _errorMessages.tryEmit("Not connected to server")
            return
        }

        // If we have a saved lobby ID, we subscribe to it
        _createdLobbyId.value?.takeIf { it.isNotBlank() }?.let { lobbyId ->
            subscribeToSpecificLobbyTopics(lobbyId)
            return
        }

        // Otherwise, request active lobby
        val request = GetActiveLobbiesRequest()
        val payload = gson.toJson(request)
        sendRequest(APP_GET_ACTIVE_LOBBIES, payload)
    }

    @SuppressLint("CheckResult")
    private fun subscribeToSpecificLobbyTopics(lobbyId: String) {
        logMessage("Subscribing to topics for lobby: $lobbyId")

        // Subscribe to lobby updates
        val lobbyUpdateTopicPath = "$TOPIC_LOBBY_UPDATES_PREFIX$lobbyId"
        stompClient?.topic(lobbyUpdateTopicPath)?.subscribe({ stompMessage: StompMessage ->
            try {
                val lobby = gson.fromJson(stompMessage.payload, Lobby::class.java)
                _lobbyState.value = lobby
                logMessage("Received lobby update for ${lobby.id} with ${lobby.players.size} players")

                if (lobby.id.isNotBlank() && lobby.id != LobbyStatus.CREATING.text) {
                    _createdLobbyId.value = lobby.id

                    // Check if we need to subscribe to game started topic
                    subscribeToGameStartedTopic(lobby.id)
                }
            } catch (e: Exception) {
                logMessage("Error parsing lobby update: ${e.message}")
            }
        }, { error ->
            logMessage("Error in lobby subscription: ${error.message}")
        })

        // Always subscribe to game started topic
        subscribeToGameStartedTopic(lobbyId)

        // Subscribe to turn-based topics for this lobby
        subscribeToTurnBasedTopics(lobbyId)
    }

    @SuppressLint("CheckResult")
    private fun subscribeToGameStartedTopic(lobbyId: String) {
        val gameStartedTopicPath = "$TOPIC_GAME_STARTED_PREFIX$lobbyId"
        Log.i("START","Subscribing to game started topic: $gameStartedTopicPath")

        stompClient?.topic(gameStartedTopicPath)?.subscribe({ stompMessage: StompMessage ->
            try {
                val response = gson.fromJson(stompMessage.payload, GameStartedResponse::class.java)
                Log.i("START","Received game started event for lobby ${response.lobbyId} with ${response.players.size} players")

                // Update game state for all players
                _gameState.value = response
                _gameStarted.value = true

                // Log all players in the game
                response.players.forEach { player ->
                    if(player.name.equals(_player.value?.name)){
                        _player.value = player
                    }
                    Log.i("START","Player in game: ${player.name} (${player.character})")
                }

                // Force a delay to ensure UI updates before navigation
                Handler(Looper.getMainLooper()).postDelayed({
                    // Double-check that we're still in the game state
                    if (_gameStarted.value) {
                        Log.e("START","Confirming game started state after delay")
                    }
                }, 500)
            } catch (e: Exception) {
                Log.e("START","Error parsing game started message: ${e.message}")
            }
        }, { error ->
            Log.e("START","Error in game started subscription: ${error.message}")
        })
    }

    fun logMessage(message: String) {
        _errorMessages.tryEmit(message)
    }

    fun setGameStarted(started: Boolean) {
        _gameStarted.value = started
        logMessage("Game started state set to: $started")
    }

    @SuppressLint("CheckResult")
    private fun sendRequest(destination: String, payload: String, onSuccess: (() -> Unit)? = null) {
        if (!_isConnected.value) {
            _errorMessages.tryEmit("Cannot send request: Not connected")
            return
        }
        stompClient?.send(destination, payload)?.subscribe(
            {
                onSuccess?.invoke()
                _errorMessages.tryEmit("Successfully sent message to $destination")
            },
            { error -> _errorMessages.tryEmit("Failed to send STOMP message to $destination: ${error.message}") }
        )
    }

    fun createLobby(username: String, character: String = "Red", color: PlayerColor = PlayerColor.RED) {
        if (!_isConnected.value) return
        val player = Player(name = username, character = character, color = color)
        val request = CreateLobbyRequest(player)
        val payload = gson.toJson(request)

        _lobbyState.value = Lobby(id = LobbyStatus.CREATING.text, host = player, players = listOf(player))
        _player.value = player;
        _createdLobbyId.value = null
        sendRequest(APP_CREATE_LOBBY, payload)
    }

    fun joinLobby(lobbyId: String, username: String, character: String = "Blue", color: PlayerColor = PlayerColor.BLUE) {
        if (!_isConnected.value || lobbyId.isBlank()) return

        _createdLobbyId.value = lobbyId
        subscribeToSpecificLobbyTopics(lobbyId)

        val player = Player(name = username, character = character, color = color)
        val request = JoinLobbyRequest(player)
        val payload = gson.toJson(request)
        val destination = "$APP_JOIN_LOBBY_PREFIX$lobbyId"

        _lobbyState.value?.let { currentLobby ->
            if (currentLobby.id == lobbyId && !currentLobby.players.any { it.name == player.name }) {
                _lobbyState.value = currentLobby.copy(players = currentLobby.players + player)
            }
        }
        _player.value = player
        sendRequest(destination, payload)
    }

    fun leaveLobby(lobbyId: String, username: String, character: String = "Blue", color: PlayerColor = PlayerColor.BLUE) {
        if (!_isConnected.value || lobbyId.isBlank()) return
        val player = Player(name = username, character = character, color = color)
        val request = LeaveLobbyRequest(player)
        val payload = gson.toJson(request)
        val destination = "$APP_LEAVE_LOBBY_PREFIX$lobbyId"

        _lobbyState.value?.let { currentLobby ->
            if (currentLobby.id == lobbyId) {
                val updatedPlayers = currentLobby.players.filterNot { it.name == player.name }
                if (updatedPlayers.isEmpty() || currentLobby.host.name == username) {
                    _lobbyState.value = null
                    _createdLobbyId.value = null
                } else {
                    _lobbyState.value = currentLobby.copy(players = updatedPlayers)
                }
            }
        }
        sendRequest(destination, payload)
    }

    @SuppressLint("CheckResult")
    fun checkCanStartGame(lobbyId: String) {
        if (!_isConnected.value || lobbyId.isBlank()) return
        val destination = "$APP_CAN_START_GAME_PREFIX$lobbyId"
        val topicPath = "$TOPIC_CAN_START_GAME_PREFIX$lobbyId"

        stompClient?.topic(topicPath)?.subscribe { stompMessage: StompMessage ->
            val response = gson.fromJson(stompMessage.payload, CanStartGameResponse::class.java)
            _canStartGame.value = response.canStart
        }
        sendRequest(destination, "")
    }

    @SuppressLint("CheckResult")
    fun startGame(lobbyId: String, username: String, character: String, color: PlayerColor) {
        if (!_isConnected.value || lobbyId.isBlank()) {
            _errorMessages.tryEmit("Cannot start game: Not connected or invalid lobby ID")
            return
        }

        // Make sure we're subscribed to the game started topic for this lobby
        subscribeToSpecificLobbyTopics(lobbyId)

        val player = Player(name = username, character = character, color = color)
        val request = StartGameRequest(player)
        val payload = gson.toJson(request)
        val destination = "$APP_START_GAME_PREFIX$lobbyId"

        logMessage("Sending start game request for lobby: $lobbyId")

        // Create a temporary game state with the current lobby players
        // This helps ensure all players see the game state even if they miss the server message
        _lobbyState.value?.let { lobby ->
            if (lobby.players.size >= 3) {
                logMessage("Creating temporary game state with ${lobby.players.size} players")
                val tempGameState = GameStartedResponse(
                    lobbyId = lobbyId,
                    players = lobby.players
                )
                _gameState.value = tempGameState
                _gameStarted.value = true  // Set this to true immediately for all players

                // Broadcast to all players
                broadcastGameStarted(tempGameState)
            }
        }
        stompClient?.send(destination, payload)?.subscribe(
            {
            },
            { error ->
                _errorMessages.tryEmit("Failed to leave lobby: ${error.message}")
            }
        )
    }

    private val _diceOneResult = MutableStateFlow<Int?>(null)
    private val _diceTwoResult = MutableStateFlow<Int?>(null)

    val diceOneResult: StateFlow<Int?> = _diceOneResult
    val diceTwoResult: StateFlow<Int?> = _diceTwoResult

    @SuppressLint("CheckResult")
    private fun subscribeToDiceResultTopic() {
        stompClient?.topic(TOPIC_DICE_RESULT)?.subscribe({ stompMessage ->
            try {
                val result = gson.fromJson(stompMessage.payload, DiceResult::class.java)
                _diceOneResult.value = result.diceOne
                _diceTwoResult.value = result.diceTwo
            } catch (e: Exception) {
                _errorMessages.tryEmit("Invalid result format: ${e.message}")
            }
        }, {
            _errorMessages.tryEmit("Error subscribing to diceResult topic")
        })
    }

    @SuppressLint("CheckResult")
    fun rollDice() {
        if (!_isConnected.value) {
            _errorMessages.tryEmit("Not connected to server")
            return
        }

        stompClient?.send(APP_ROLL_DICE, "")?.subscribe({
            _errorMessages.tryEmit("Dice requested")
        }, { error ->
            _errorMessages.tryEmit("Error from rolling the dice: ${error.message}")
        })
    }

    private var playerList: List<Player>? = null
    public fun getPlayers(): List<Player>? {
        return playerList;
    }

    @SuppressLint("CheckResult")
    private fun subscribePlayersResult() {
        stompClient?.topic(TOPIC_GET_PLAYERS)?.subscribe({ stompMessage ->
            try {
                val result = gson.fromJson(stompMessage.payload, List::class.java)
                playerList = result as? List<Player>
            } catch (e: Exception) {
                _errorMessages.tryEmit("Invalid result format: ${e.message}")
            }
        }, {
            _errorMessages.tryEmit("Error subscribing to diceResult topic")
        })
    }

    @SuppressLint("CheckResult")
    fun players() {
        println("Get Players")
        if (!_isConnected.value) {
            _errorMessages.tryEmit("Not connected to server")
            return
        }
        stompClient?.send(APP_GET_PLAYERS, "")?.subscribe(
            {
                _errorMessages.tryEmit("Players Requested")
            },
            { error -> _errorMessages.tryEmit("Error from trying to get all Players: ${error.message}") })

    }

    @SuppressLint("CheckResult")
    fun performMovement(lobbyId:String,moves: List<String>) {
        println("Movein")
        if (!_isConnected.value) {
            _errorMessages.tryEmit("Not connected to server")
            return
        }
        val request = PerformMoveResponse(player = player.value!!, moves=moves)
        val payload = gson.toJson(request)
        val destination = "$APP_PERFORM_MOVE${lobbyId}"

        stompClient?.send(destination, payload)?.subscribe(
            {
                _errorMessages.tryEmit("PerformMovement")
            },
            { error -> _errorMessages.tryEmit("Error from trying to get all Players: ${error.message}") })
        //subscribeGetGameData(lobbyId)

    }
    fun subscribeToMovementUpdates(lobbyId: String,callback: (GameData) -> Unit) {
        val topic = "/topic/performMovement/$lobbyId"

        stompClient?.topic(topic)?.subscribe { stompMessage ->
            val payload = stompMessage.payload
            val gameData = gson.fromJson(payload, GameData::class.java)
            callback(gameData)
            Log.d("STOMP", "Received movement update: $gameData")
        }
    }


    /**
     * Check if a game has started for the current lobby
     * This is especially useful for non-host players
     */
    fun checkGameStarted() {
        if (!_isConnected.value) {
            logMessage("Cannot check game started: Not connected")
            return
        }

        // If we already have a game state, use it
        if (_gameState.value != null) {
            _gameStarted.value = true
            return
        }

        // Try to use the lobby state
        _lobbyState.value?.let { lobby ->
            if (lobby.id.isNotBlank() && lobby.id != LobbyStatus.CREATING.text) {
                logMessage("Checking if game has started for lobby: ${lobby.id}")

                // Make sure we're subscribed to the game started topic
                subscribeToGameStartedTopic(lobby.id)

                // Request the current game state
                val destination = "$APP_CAN_START_GAME_PREFIX${lobby.id}"
                sendRequest(destination, "") {
                    logMessage("Sent request to check if game has started")
                }
            }
        }
    }

    /**
     * Broadcast game started event to all players
     * This is a helper method to ensure all players receive the game state
     */
    private fun broadcastGameStarted(gameState: GameStartedResponse) {
        logMessage("Broadcasting game started to all players")

        // Set the game state for all players
        _gameState.value = gameState
        _gameStarted.value = true
    }


    @SuppressLint("CheckResult")
    fun solveCase(lobbyId: String, username: String, suspect: String, room: String, weapon: String) {
        val request = SolveCaseRequest(lobbyId, username, suspect, room, weapon)
        val payload = gson.toJson(request)
        stompClient?.send("/app/solve-case", payload)?.subscribe()
    }

    fun gameData(lobbyId: String,player: Player) {
        if (!_isConnected.value || lobbyId.isBlank()) {
            _errorMessages.tryEmit("Cannot get game Data: Not connected or invalid lobby ID")
            return
        }


        val request = StartGameRequest(player)
        val payload = gson.toJson(request)
        val destination = "$APP_GET_GAME_DATA$lobbyId"
        //subscribeGetGameData(lobbyId)
        logMessage("Sending get game Data request for lobby: $lobbyId")

        // Create a temporary game state with the current lobby players
        // This helps ensure all players see the game state even if they miss the server message
        _lobbyState.value?.let { lobby ->
            if (lobby.players.size >= 3) {
                logMessage("Creating temporary game state with ${lobby.players.size} players")
                val tempGameState = GameData(
                    players = lobby.players,
                )
                _gameDataState.value = tempGameState
            }
        }
        stompClient?.send(destination, payload)?.subscribe(
            {
            },
            { error ->
                _errorMessages.tryEmit("Failed to leave lobby: ${error.message}")
            }
        )
    }

    @SuppressLint("CheckResult")
    fun isWall(lobbyId:String, x:Int, y:Int) {

        val request = IsWallRequest(x,y)
        val payload = gson.toJson(request)
        val destination = "$APP_IS_WALL$lobbyId"
        stompClient?.send(destination, payload)?.subscribe(
            {

            },
            { error ->
                _errorMessages.tryEmit("Failed to leave lobby: ${error.message}")
            }
        )
    }

    @SuppressLint("CheckResult")
    fun subscribeIsWall(lobbyId: String, onResult: (Boolean) -> Unit) {
        val source = "$TOPIC_IS_WALL$lobbyId"
        var disposable: Disposable? = null
            disposable=stompClient?.topic(source)?.subscribe({ stompMessage ->
            try {
                val response = gson.fromJson(stompMessage.payload, Boolean::class.java)
                Log.d("Debug", "Wall: $response")
                onResult(response)
            } catch (e: Exception) {
                logMessage("Error parsing isWall message: ${e.message}")
                onResult(false)
            } finally {
                // Abo nach *einer* Nachricht entfernen
                disposable?.dispose()
            }
        }, { error ->
            logMessage("Error in isWall subscription: ${error.message}")
            onResult(false)
            disposable?.dispose()
        })
        }

    @SuppressLint("CheckResult")
    fun subscribeGetGameData(lobbyId: String,callback: (GameData) -> Unit) {
        val gameStartedTopicPath = "$TOPIC_GAME_DATA_PREFIX$lobbyId"
        logMessage("Subscribing to game started topic: $gameStartedTopicPath")

        stompClient?.topic(gameStartedTopicPath)?.subscribe({ stompMessage: StompMessage ->
            try {
                val response = gson.fromJson(stompMessage.payload, GameData::class.java)
                logMessage("Received game started event for lobby ${response.players} with  players")

                // Update game state for all players
                callback(response)
                _gameDataState.value=response // Log all players in the game
                _gameState.value?.players=response.players
                _lobbyState.value?.players =response.players

                response.players.forEach { player ->
                    logMessage("Player in game: ${player.name} (${player.character})")
                }

                // Force a delay to ensure UI updates before navigation
                Handler(Looper.getMainLooper()).postDelayed({
                }, 500)
            } catch (e: Exception) {
                logMessage("Error parsing game started message: ${e.message}")
            }
        }, { error ->
            logMessage("Error in game started subscription: ${error.message}")
        })

    }

    /**
     * Subscribe to all turn-based topics for a specific lobby
     */
    @SuppressLint("CheckResult")
    private fun subscribeToTurnBasedTopics(lobbyId: String) {
        logMessage("Subscribing to turn-based topics for lobby: $lobbyId")

        // subscribe to turn state updates
        val turnStateTopicPath = "$TOPIC_CURRENT_TURN_STATE$lobbyId"
        stompClient?.topic(turnStateTopicPath)?.subscribe({ stompMessage ->
            try {
                val turnState = gson.fromJson(stompMessage.payload, TurnStateResponse::class.java)
                _currentTurnState.value = turnState
                updatePlayerTurnStatus(turnState)
                logMessage("Turn state updated: ${turnState.currentPlayerName}'s turn (${turnState.turnState})")
            } catch (e: Exception) {
                _errorMessages.tryEmit("Error parsing turn state update: ${e.message}")
            }
        }, { error ->
            _errorMessages.tryEmit("Error subscribing to turn state updates: ${error.message}")
        })

        // subscribe to turn initialization
        val turnsInitializedTopicPath = "$TOPIC_TURNS_INITIALIZED$lobbyId"
        stompClient?.topic(turnsInitializedTopicPath)?.subscribe({ stompMessage ->
            try {
                val turnState = gson.fromJson(stompMessage.payload, TurnStateResponse::class.java)
                _currentTurnState.value = turnState
                updatePlayerTurnStatus(turnState)
                logMessage("Turns initialized: ${turnState.currentPlayerName}'s turn (${turnState.turnState})")
            } catch (e: Exception) {
                _errorMessages.tryEmit("Error parsing turn initialization: ${e.message}")
            }
        }, { error ->
            _errorMessages.tryEmit("Error subscribing to turn initialization: ${error.message}")
        })

        // subscribe to dice rolled responses
        val diceRolledTopicPath = "$TOPIC_DICE_ROLLED$lobbyId"
        stompClient?.topic(diceRolledTopicPath)?.subscribe({ stompMessage ->
            try {
                logMessage("DEBUG: Raw dice roll response: ${stompMessage.payload}")

                // Parse the response as a generic map first to check the format
                val responseMap = gson.fromJson(stompMessage.payload, Map::class.java)
                logMessage("DEBUG: Parsed response map: $responseMap")
                Log.d("WebSocketService", "DEBUG: Parsed response map: $responseMap")

                // Check if this is a TurnStateResponse (has lobbyId and currentPlayerName)
//                if (responseMap.containsKey("lobbyId") && responseMap.containsKey("currentPlayerName")) {
//                    // This is the full TurnStateResponse format
//                    val turnState = gson.fromJson(stompMessage.payload, TurnStateResponse::class.java)
//                    _currentTurnState.value = turnState
//                    updatePlayerTurnStatus(turnState)
//
//                    // Update dice values if they're included in the response
//                    if (turnState.diceValue > 0) {
//                        _diceOneResult.value = turnState.diceValue
//                        _diceTwoResult.value = 0 // Turn-based might only use one die
//                        logMessage("DEBUG: Updated dice display with value: ${turnState.diceValue}")
//                    }
//
//                    logMessage("Dice rolled: ${turnState.diceValue}, new state: ${turnState.turnState}, current player: ${turnState.currentPlayerName}")
//
//                } else
                if (responseMap.containsKey("player") && responseMap.containsKey("diceValue") && responseMap.containsKey("turnState")) {
                    // This is the simplified dice response format
                    val diceValue = (responseMap["diceValue"] as? Double)?.toInt() ?: 0
                    val turnStateRaw = responseMap["turnState"]
                    val turnStateStr = turnStateRaw?.toString() ?: ""
                    val player = responseMap["player"] as? String ?: ""

                    logMessage("DEBUG: Simplified dice response - player: $player, diceValue: $diceValue, turnState: $turnStateStr")
                    Log.d("WebSocketService", "DEBUG: Simplified dice response - player: $player, diceValue: $diceValue, turnState: $turnStateStr")

                    // Update dice display
                    if (diceValue > 0) {
                        _diceOneResult.value = diceValue
                        _diceTwoResult.value = 0
                        logMessage("DEBUG: Updated dice display with value: $diceValue")
                    }

                    // Create or update turn state
                    val currentTurnState = _currentTurnState.value
                    val updatedTurnState = if (currentTurnState != null) {
                        currentTurnState.copy(
                            currentPlayerName = player,
                            turnState = turnStateStr,
                            diceValue = diceValue
                        )
                    } else {
                        TurnStateResponse(
                            lobbyId = lobbyId,
                            currentPlayerName = player,
                            turnState = turnStateStr,
                            diceValue = diceValue
                        )
                    }

                    _currentTurnState.value = updatedTurnState
                    updatePlayerTurnStatus(updatedTurnState)
                    logMessage("Dice rolled: $diceValue, new state: $turnStateStr, current player: $player")

                } else {
                    logMessage("DEBUG: Unknown dice response format: ${stompMessage.payload}")
                }

            } catch (e: Exception) {
                logMessage("Error parsing dice roll response: ${e.message}")
                _errorMessages.tryEmit("Error parsing dice roll response: ${e.message}")
            }
        }, { error ->
            _errorMessages.tryEmit("Error subscribing to dice roll response: ${error.message}")
        })

        // Subscribe to movement completion responses
        val movementCompletedTopicPath = "$TOPIC_MOVEMENT_COMPLETED$lobbyId"
        stompClient?.topic(movementCompletedTopicPath)?.subscribe({ stompMessage ->
            try {
                val turnState = gson.fromJson(stompMessage.payload, TurnStateResponse::class.java)
                _currentTurnState.value = turnState
                updatePlayerTurnStatus(turnState)
                logMessage("Movement completed, new state: ${turnState.turnState}, current player: ${turnState.currentPlayerName}")
            } catch (e: Exception) {
                _errorMessages.tryEmit("Error parsing movement completion response: ${e.message}")
            }
        }, { error ->
            _errorMessages.tryEmit("Error subscribing to movement completion response: ${error.message}")
        })
    }

    // ========== TURN-BASED SYSTEM METHODS ==========

    /**
     * Initialize the turn system for a lobby when the game starts
     */
    @SuppressLint("CheckResult")
    fun initializeTurns(lobbyId: String) {
        if (!_isConnected.value || lobbyId.isBlank()) {
            _errorMessages.tryEmit("Cannot initialize turns: Not connected or invalid lobby ID")
            return
        }

        val destination = "$APP_INITIALIZE_TURNS$lobbyId"

        // Don't create duplicate subscription - the turn-based topics are already subscribed to
        // in subscribeToTurnBasedTopics() which is called from subscribeToSpecificLobbyTopics()
        logMessage("Sending turn initialization request for lobby: $lobbyId")
        sendRequest(destination, "")
    }

    /**
     * Get the current turn state for a lobby
     */
    @SuppressLint("CheckResult")
    fun getTurnState(lobbyId: String) {
        if (!_isConnected.value || lobbyId.isBlank()) {
            _errorMessages.tryEmit("Cannot get turn state: Not connected or invalid lobby ID")
            return
        }

        val destination = "$APP_GET_TURN_STATE$lobbyId"

        // Don't create duplicate subscription - the turn-based topics are already subscribed to
        // in subscribeToTurnBasedTopics() which is called from subscribeToSpecificLobbyTopics()
        logMessage("Requesting current turn state for lobby: $lobbyId")
        sendRequest(destination, "")
    }

    /**
     * Check if it's a specific player's turn
     */
    @SuppressLint("CheckResult")
    fun checkPlayerTurn(lobbyId: String, playerName: String) {
        if (!_isConnected.value || lobbyId.isBlank() || playerName.isBlank()) {
            _errorMessages.tryEmit("Cannot check player turn: Invalid parameters")
            return
        }

        val destination = "$APP_CHECK_PLAYER_TURN$lobbyId"
        val topicPath = "$TOPIC_PLAYER_TURN_CHECK$lobbyId"

        // Subscribe to player turn check response
        stompClient?.topic(topicPath)?.subscribe({ stompMessage ->
            try {
                val response = gson.fromJson(stompMessage.payload, Map::class.java)
                val isPlayerTurn = response["isPlayerTurn"] as? Boolean ?: false
                _isCurrentPlayerTurn.value = isPlayerTurn
                logMessage("Player turn check: $playerName - $isPlayerTurn")
            } catch (e: Exception) {
                _errorMessages.tryEmit("Error parsing player turn check: ${e.message}")
            }
        }, { error ->
            _errorMessages.tryEmit("Error subscribing to player turn check: ${error.message}")
        })

        val payload = gson.toJson(mapOf("playerName" to playerName))
        sendRequest(destination, payload)
    }

    /**
     * Update player turn status based on turn state response
     */
    private fun updatePlayerTurnStatus(turnState: TurnStateResponse) {
        val currentPlayerName = _player.value?.name
        val isMyTurn = currentPlayerName == turnState.currentPlayerName
        val previousTurnStatus = _isCurrentPlayerTurn.value

        // Force update the turn status even if it's the same value to trigger UI updates
        _isCurrentPlayerTurn.value = isMyTurn

        logMessage("DEBUG: Turn status update - myName: $currentPlayerName, currentPlayer: ${turnState.currentPlayerName}, isMyTurn: $isMyTurn (was: $previousTurnStatus), state: ${turnState.turnState}")
        Log.d("WebSocketService", "DEBUG: Turn status update - myName: $currentPlayerName, currentPlayer: ${turnState.currentPlayerName}, isMyTurn: $isMyTurn (was: $previousTurnStatus), state: ${turnState.turnState}")

        // Log additional debugging information
        logMessage("DEBUG: Current turn state details - lobbyId: ${turnState.lobbyId}, diceValue: ${turnState.diceValue}, canMakeAccusation: ${turnState.canMakeAccusation}, canMakeSuggestion: ${turnState.canMakeSuggestion}")
        Log.d("WebSocketService", "DEBUG: Current turn state details - lobbyId: ${turnState.lobbyId}, diceValue: ${turnState.diceValue}, canMakeAccusation: ${turnState.canMakeAccusation}, canMakeSuggestion: ${turnState.canMakeSuggestion}")

        if (isMyTurn) {
            logMessage("It's your turn! State: ${turnState.turnState}")
        } else {
            logMessage("It's ${turnState.currentPlayerName}'s turn. State: ${turnState.turnState}")
        }

        // Force a small delay and then trigger another update to ensure UI refreshes
        Handler(Looper.getMainLooper()).postDelayed({
            if (_isCurrentPlayerTurn.value != isMyTurn) {
                logMessage("DEBUG: Turn status mismatch detected, forcing update")
                _isCurrentPlayerTurn.value = isMyTurn
            }
        }, 100)
    }

    // ========== TURN-BASED ACTION METHODS ==========

    /**
     * Roll dice for the current player's turn
     */
    @SuppressLint("CheckResult")
    fun rollDiceForTurn(lobbyId: String) {
        val playerName = _player.value?.name
        if (!_isConnected.value || lobbyId.isBlank() || playerName.isNullOrBlank()) {
            _errorMessages.tryEmit("Cannot roll dice: Invalid parameters")
            return
        }

        logMessage("DEBUG: Rolling dice for $playerName in lobby $lobbyId")

        // Don't create a new subscription here - the dice rolled topic is already subscribed to
        // in subscribeToTurnBasedTopics() which handles all turn-based responses

        val request = TurnActionRequest(
            playerName = playerName,
            actionType = "DICE_ROLL",
            diceValue = 0 // Server generates the value
        )
        val payload = gson.toJson(request)
        val destination = "$APP_ROLL_DICE_TURN$lobbyId"

        sendRequest(destination, payload) {
            logMessage("Dice roll request sent for $playerName")
        }
    }

    /**
     * Complete movement and advance turn state
     */
    @SuppressLint("CheckResult")
    fun completeMovement(lobbyId: String) {
        val playerName = _player.value?.name
        if (!_isConnected.value || lobbyId.isBlank() || playerName.isNullOrBlank()) {
            _errorMessages.tryEmit("Cannot complete movement: Invalid parameters")
            return
        }

        // Don't create duplicate subscription - movement completion responses should be handled
        // by the general turn state subscription in subscribeToTurnBasedTopics()

        val request = TurnActionRequest(
            playerName = playerName,
            actionType = "COMPLETE_MOVEMENT"
        )
        val payload = gson.toJson(request)
        val destination = "$APP_COMPLETE_MOVEMENT$lobbyId"

        sendRequest(destination, payload) {
            logMessage("Movement completion request sent for $playerName")
        }
    }

    /**
     * Make a suggestion during gameplay
     */
    @SuppressLint("CheckResult")
    fun makeSuggestion(lobbyId: String, suspect: String, weapon: String, room: String) {
        val playerName = _player.value?.name
        if (!_isConnected.value || lobbyId.isBlank() || playerName.isNullOrBlank()) {
            _errorMessages.tryEmit("Cannot make suggestion: Invalid parameters")
            return
        }

        // Subscribe to suggestion response for this lobby
        val topicPath = "$TOPIC_SUGGESTION_MADE$lobbyId"
        stompClient?.topic(topicPath)?.subscribe({ stompMessage ->
            try {
                // The response might be a Map<String, Object> as per documentation
                val response = gson.fromJson(stompMessage.payload, Map::class.java)
                logMessage("Suggestion response received for $playerName: $response")

                // If the response contains turn state information, update it
                response["turnState"]?.let { turnStateData ->
                    try {
                        val turnStateJson = gson.toJson(turnStateData)
                        val turnState = gson.fromJson(turnStateJson, TurnStateResponse::class.java)
                        _currentTurnState.value = turnState
                        updatePlayerTurnStatus(turnState)
                    } catch (e: Exception) {
                        logMessage("Could not parse turn state from suggestion response: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                _errorMessages.tryEmit("Error parsing suggestion response: ${e.message}")
            }
        }, { error ->
            _errorMessages.tryEmit("Error subscribing to suggestion response: ${error.message}")
        })

        val request = SuggestionRequest(
            playerName = playerName,
            suspect = suspect,
            weapon = weapon,
            room = room
        )
        val payload = gson.toJson(request)
        val destination = "$APP_MAKE_SUGGESTION$lobbyId"

        sendRequest(destination, payload) {
            logMessage("Suggestion request sent for $playerName: $suspect with $weapon in $room")
        }
    }

    /**
     * Check if the current player can perform a specific action based on turn state
     */
    fun canPerformAction(action: String): Boolean {
        val turnState = _currentTurnState.value
        val isMyTurn = _isCurrentPlayerTurn.value

        if (!isMyTurn || turnState == null) {
            logMessage("DEBUG: canPerformAction($action) = false - isMyTurn: $isMyTurn, turnState: ${turnState?.turnState}")
            return false
        }

        val result = when (action) {
            "ROLL_DICE" -> {
                val canRoll = turnState.turnState == TurnState.PLAYERS_TURN_ROLL_DICE.value
                logMessage("DEBUG: ROLL_DICE check - turnState: '${turnState.turnState}' == '${TurnState.PLAYERS_TURN_ROLL_DICE.value}' = $canRoll")
                canRoll
            }
            "MOVE" -> {
                val canMove = turnState.turnState == TurnState.PLAYERS_TURN_MOVE.value
                logMessage("DEBUG: MOVE check - turnState: '${turnState.turnState}' == '${TurnState.PLAYERS_TURN_MOVE.value}' = $canMove")
                Log.d("WebSocketService", "DEBUG: MOVE check - turnState: '${turnState.turnState}' == '${TurnState.PLAYERS_TURN_MOVE.value}' = $canMove")
                canMove
            }
            "SUGGEST" -> turnState.turnState == TurnState.PLAYERS_TURN_SUSPECT.value && (turnState.canMakeSuggestion == true)
            "ACCUSE" -> turnState.canMakeAccusation == true
            else -> false
        }

        logMessage("DEBUG: canPerformAction($action) = $result - turnState: ${turnState.turnState}, isMyTurn: $isMyTurn")
        return result
    }
}